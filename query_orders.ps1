$params = @{`n    fields = '*,Phone_Card.*,channel_config.*'`n    filter = '{"_and":[{"logisticsStatus":{"_in":["发货中","发货成功"]}},{"Phone_Card.card_typ":{"_eq":"骑手卡"}},{"createTime":{"_between":["2025-07-30 00:00:00","2025-07-30 23:59:59"]}}]}'`n    sort = 'createTime'`n}`n`n$query = New-Object System.Collections.Specialized.NameValueCollection`nforeach ($key in $params.Keys) {`n    $query.Add($key, $params[$key])`n}`n`n$uriBuilder = New-Object System.UriBuilder('http://localhost:8055/items/forders')`n$uriBuilder.Query = [System.Web.HttpUtility]::ParseQueryString('').ToString()`nforeach ($key in $query.AllKeys) {`n    $uriBuilder.Query += if ($uriBuilder.Query -eq '') { '' } else { '&' } + [System.Web.HttpUtility]::UrlEncode($key) + '=' + [System.Web.HttpUtility]::UrlEncode($query[$key])`n}`n`n$headers = @{`n    "Content-Type" = "application/json"`n    "Authorization" = "Bearer {{auth_token}}"`n}`n`nInvoke-WebRequest -Uri $uriBuilder.Uri -Method GET -Headers $headers